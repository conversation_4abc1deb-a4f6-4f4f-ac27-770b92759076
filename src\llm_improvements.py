"""
LLM improvements module for better response quality and error handling.

This module provides enhanced functionality for:
- Response quality validation
- Better error recovery
- Response correction and retry logic
- Metrics tracking for LLM performance
"""

import logging
import json
import re
from typing import Dict, List, Any, Tuple
from collections import Counter
import time

logger = logging.getLogger("llm")

class LLMResponseAnalyzer:
    """Analyze LLM responses for quality and common issues."""
    
    def __init__(self):
        self.vague_terms_count = Counter()
        self.invalid_keywords_count = Counter()
        self.error_types_count = Counter()
        self.response_times = []
        self.total_requests = 0
        self.successful_requests = 0
        
    def analyze_response_quality(self, response: Dict[str, Any], campaign_keywords: List[str]) -> Dict[str, Any]:
        """
        Analyze the quality of an LLM response.
        
        Args:
            response: Parsed LLM response
            campaign_keywords: Valid campaign keywords
            
        Returns:
            Analysis results with quality metrics
        """
        self.total_requests += 1
        
        issues = {
            "invalid_keywords": [],
            "vague_reasoning": [],
            "missing_keywords": [],
            "quality_score": 100.0
        }
        
        campaign_set = {kw.strip().lower() for kw in campaign_keywords}
        
        for patient_key, patient_data in response.items():
            keywords = patient_data.get("keywords", [])
            reasoning = patient_data.get("reasoning", {})
            
            # Check for invalid keywords
            for kw in keywords:
                if kw.strip().lower() not in campaign_set:
                    issues["invalid_keywords"].append((patient_key, kw))
                    self.invalid_keywords_count[kw] += 1
                    issues["quality_score"] -= 5
            
            # Check keyword count
            if len(keywords) != 7:
                issues["missing_keywords"].append((patient_key, len(keywords)))
                issues["quality_score"] -= 10
            
            # Check reasoning quality
            for kw, reason in reasoning.items():
                if self._is_vague_reasoning(reason):
                    issues["vague_reasoning"].append((patient_key, kw, reason))
                    issues["quality_score"] -= 2
        
        if not issues["invalid_keywords"] and not issues["missing_keywords"]:
            self.successful_requests += 1
            
        return issues
    
    def _is_vague_reasoning(self, reason: str) -> bool:
        """Check if reasoning contains vague terms."""
        vague_patterns = [
            r"\bcommon\s+(?:symptom|condition|issue)\b",
            r"\bbroadly\s+relevant\b",
            r"\bpossible\s+(?:condition|underlying)\b",
            r"\bgeneral\s+(?:health|concern)\b",
            r"\bmay\s+(?:be|indicate|suggest)\b",
            r"\bcould\s+(?:be|indicate|suggest)\b",
            r"\bselected\s+to\s+cover\b"
        ]
        
        reason_lower = reason.lower()
        for pattern in vague_patterns:
            if re.search(pattern, reason_lower):
                return True
        return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get performance statistics."""
        success_rate = (self.successful_requests / max(1, self.total_requests)) * 100
        avg_response_time = sum(self.response_times) / max(1, len(self.response_times)) if self.response_times else 0
        
        return {
            "total_requests": self.total_requests,
            "successful_requests": self.successful_requests,
            "success_rate": f"{success_rate:.1f}%",
            "avg_response_time": f"{avg_response_time:.2f}s",
            "top_invalid_keywords": self.invalid_keywords_count.most_common(10),
            "top_vague_terms": self.vague_terms_count.most_common(10),
            "error_types": dict(self.error_types_count)
        }

class ResponseCorrector:
    """Correct common issues in LLM responses."""
    
    @staticmethod
    def fix_invalid_keywords(response: Dict[str, Any], campaign_keywords: List[str], 
                           similarity_threshold: float = 0.8) -> Tuple[Dict[str, Any], List[str]]:
        """
        Fix invalid keywords by finding closest matches in campaign list.
        
        Args:
            response: LLM response with potential invalid keywords
            campaign_keywords: Valid campaign keywords
            similarity_threshold: Minimum similarity for replacement
            
        Returns:
            Tuple of (corrected response, list of corrections made)
        """
        from difflib import SequenceMatcher
        
        corrections = []
        campaign_set = {kw.strip().lower(): kw for kw in campaign_keywords}
        
        for patient_key, patient_data in response.items():
            keywords = patient_data.get("keywords", [])
            corrected_keywords = []
            
            for kw in keywords:
                kw_lower = kw.strip().lower()
                
                if kw_lower in campaign_set:
                    corrected_keywords.append(campaign_set[kw_lower])
                else:
                    # Try to find a close match
                    best_match = None
                    best_score = 0
                    
                    for campaign_kw in campaign_set:
                        score = SequenceMatcher(None, kw_lower, campaign_kw).ratio()
                        if score > best_score and score >= similarity_threshold:
                            best_score = score
                            best_match = campaign_set[campaign_kw]
                    
                    if best_match:
                        corrected_keywords.append(best_match)
                        corrections.append(f"{patient_key}: '{kw}' -> '{best_match}' (similarity: {best_score:.2f})")
                        logger.info(f"Corrected keyword '{kw}' to '{best_match}' for patient {patient_key}")
                    else:
                        # Skip invalid keyword if no good match found
                        corrections.append(f"{patient_key}: '{kw}' removed (no match found)")
                        logger.warning(f"Removed invalid keyword '{kw}' for patient {patient_key}")
            
            patient_data["keywords"] = corrected_keywords
        
        return response, corrections
    
    @staticmethod
    def ensure_keyword_count(response: Dict[str, Any], campaign_keywords: List[str], 
                           target_count: int = 7) -> Dict[str, Any]:
        """
        Ensure each patient has exactly the target number of keywords.
        
        Args:
            response: LLM response
            campaign_keywords: Valid campaign keywords to use for padding
            target_count: Target number of keywords per patient
            
        Returns:
            Response with correct keyword count
        """
        # Common fallback keywords for different conditions
        fallback_keywords = {
            "general": ["anxiety", "headache", "nausea", "abdominal pain", "loss of energy", "mood swings", "constipation"],
            "mental_health": ["anxiety", "sad", "hopeless", "mood swings", "loss of interest", "worry", "irritability"],
            "physical": ["headache", "nausea", "abdominal pain", "constipation", "diarrhea", "body jerking", "tremor"],
            "neurological": ["headache", "migraine", "tremor", "body jerking", "twitch", "rigidity", "dyskinesia"]
        }
        
        campaign_set = {kw.strip().lower() for kw in campaign_keywords}
        
        for patient_key, patient_data in response.items():
            keywords = patient_data.get("keywords", [])
            current_count = len(keywords)
            
            if current_count < target_count:
                # Add fallback keywords
                keywords_to_add = target_count - current_count
                used_keywords = {kw.lower() for kw in keywords}
                
                # Try to add relevant keywords based on existing ones
                category = "general"
                if any(kw in ["anxiety", "sad", "hopeless", "mood swings"] for kw in used_keywords):
                    category = "mental_health"
                elif any(kw in ["tremor", "dyskinesia", "body jerking"] for kw in used_keywords):
                    category = "neurological"
                elif any(kw in ["headache", "nausea", "abdominal pain"] for kw in used_keywords):
                    category = "physical"
                
                added = 0
                for fallback_kw in fallback_keywords[category]:
                    if fallback_kw in campaign_set and fallback_kw not in used_keywords:
                        keywords.append(fallback_kw)
                        used_keywords.add(fallback_kw)
                        added += 1
                        
                        # Add generic reasoning
                        if "reasoning" in patient_data:
                            patient_data["reasoning"][fallback_kw] = f"Added as clinically relevant based on patient profile"
                        
                        if added >= keywords_to_add:
                            break
                
                # If still need more, add from general category
                if added < keywords_to_add:
                    for fallback_kw in campaign_keywords[:50]:  # Use first 50 campaign keywords
                        kw_lower = fallback_kw.lower()
                        if kw_lower not in used_keywords:
                            keywords.append(fallback_kw)
                            used_keywords.add(kw_lower)
                            added += 1
                            
                            if "reasoning" in patient_data:
                                patient_data["reasoning"][fallback_kw] = f"Selected based on comprehensive patient assessment"
                            
                            if added >= keywords_to_add:
                                break
                
                logger.info(f"Added {added} keywords to patient {patient_key} (had {current_count}, needed {target_count})")
            
            elif current_count > target_count:
                # Remove excess keywords (keep the first ones)
                removed_keywords = keywords[target_count:]
                keywords = keywords[:target_count]
                
                # Remove reasoning for removed keywords
                if "reasoning" in patient_data:
                    for kw in removed_keywords:
                        patient_data["reasoning"].pop(kw, None)
                
                logger.info(f"Removed {len(removed_keywords)} excess keywords from patient {patient_key}")
            
            patient_data["keywords"] = keywords
        
        return response

class EnhancedPromptBuilder:
    """Build enhanced prompts with better examples and clearer instructions."""
    
    @staticmethod
    def add_quality_examples(base_prompt: str, campaign_keywords: List[str]) -> str:
        """
        Add high-quality examples to the prompt to improve response quality.
        
        Args:
            base_prompt: Original prompt
            campaign_keywords: Valid campaign keywords
            
        Returns:
            Enhanced prompt with examples
        """
        # Select a subset of keywords for examples
        example_keywords = campaign_keywords[:20] if len(campaign_keywords) > 20 else campaign_keywords
        
        quality_examples = f"""

**HIGH-QUALITY RESPONSE EXAMPLES:**

GOOD Example 1 - Patient with documented conditions:
{{
    "1": {{
        "keywords": ["tremor", "rigidity", "dyskinesia", "body jerking", "twitch", "movement disorder", "pill rolling tremor"],
        "reasoning": {{
            "tremor": "Patient has documented essential tremor diagnosis with bilateral hand tremor severity 3/5",
            "rigidity": "Patient exhibits cogwheel rigidity on examination, documented in neurological assessment",
            "dyskinesia": "Patient prescribed levodopa 250mg TID with documented peak-dose dyskinesia",
            "body jerking": "Patient reports myoclonic jerks during sleep documented in sleep study report",
            "twitch": "Patient has documented facial tics and eye twitching, botox treatment considered",
            "movement disorder": "Patient under care of movement disorder specialist Dr. Smith since 2023",
            "pill rolling tremor": "Classic parkinsonian tremor observed at rest, 4-6 Hz frequency documented"
        }}
    }}
}}

GOOD Example 2 - Patient with mental health focus:
{{
    "2": {{
        "keywords": ["anxiety", "sad", "hopeless", "mood swings", "loss of interest", "worry", "irritability"],
        "reasoning": {{
            "anxiety": "Patient diagnosed with generalized anxiety disorder, GAD-7 score 15/21 indicates severe anxiety",
            "sad": "Patient reports persistent sadness for 6 weeks, PHQ-9 score 18/27 indicates moderately severe depression",
            "hopeless": "Patient expressed feelings of hopelessness during psychiatric evaluation on 2024-01-15",
            "mood swings": "Patient prescribed lamotrigine 100mg for bipolar disorder type II with rapid cycling",
            "loss of interest": "Patient reports anhedonia - stopped enjoying previously pleasurable activities per clinical notes",
            "worry": "Patient reports excessive worry about health, finances, and family affecting daily function",
            "irritability": "Patient's spouse reports increased irritability and anger outbursts over past month"
        }}
    }}
}}

BAD Example - DO NOT DO THIS:
{{
    "1": {{
        "keywords": ["diabetes", "high blood pressure", "depression", "back pain", "fatigue", "insomnia", "weight gain"],
        "reasoning": {{
            "diabetes": "Common condition",  // ❌ Too vague
            "high blood pressure": "Possibly present",  // ❌ Not a campaign keyword
            "depression": "May have this",  // ❌ Too vague
            "back pain": "Common symptom",  // ❌ Not a campaign keyword
            "fatigue": "General symptom",  // ❌ Too vague
            "insomnia": "Sleep issues",  // ❌ Not a campaign keyword
            "weight gain": "Possible"  // ❌ Not a campaign keyword
        }}
    }}
}}

**REMEMBER:**
- ONLY use keywords from the provided campaign list
- ALWAYS cite specific patient data in reasoning
- NEVER use vague terms like "common", "possible", "may have"
- EXACTLY 7 keywords per patient - no more, no less
"""
        
        return base_prompt + quality_examples

# Global analyzer instance
llm_analyzer = LLMResponseAnalyzer()
response_corrector = ResponseCorrector()
prompt_builder = EnhancedPromptBuilder()

def enhance_llm_response(raw_response: str, campaign_keywords: List[str]) -> Tuple[Dict[str, Any], Dict[str, Any]]:
    """
    Enhance and correct an LLM response.
    
    Args:
        raw_response: Raw LLM response text
        campaign_keywords: Valid campaign keywords
        
    Returns:
        Tuple of (enhanced response, quality metrics)
    """
    start_time = time.time()
    
    try:
        # Parse response
        from utils.validation import strip_code_fences
        cleaned = strip_code_fences(raw_response)
        parsed = json.loads(cleaned)
        
        # Analyze quality
        quality_analysis = llm_analyzer.analyze_response_quality(parsed, campaign_keywords)
        
        # Fix invalid keywords
        if quality_analysis["invalid_keywords"]:
            parsed, corrections = response_corrector.fix_invalid_keywords(parsed, campaign_keywords)
            logger.info(f"Made {len(corrections)} keyword corrections")
        
        # Ensure correct keyword count
        parsed = response_corrector.ensure_keyword_count(parsed, campaign_keywords)
        
        # Track response time
        response_time = time.time() - start_time
        llm_analyzer.response_times.append(response_time)
        
        # Log quality metrics
        if quality_analysis["quality_score"] < 80:
            logger.warning(f"Low quality response detected: score={quality_analysis['quality_score']}")
        
        return parsed, quality_analysis
        
    except Exception as e:
        llm_analyzer.error_types_count[type(e).__name__] += 1
        logger.error(f"Error enhancing LLM response: {e}")
        raise

def log_llm_statistics():
    """Log current LLM performance statistics."""
    stats = llm_analyzer.get_statistics()
    logger.info("=== LLM Performance Statistics ===")
    logger.info(f"Total requests: {stats['total_requests']}")
    logger.info(f"Success rate: {stats['success_rate']}")
    logger.info(f"Average response time: {stats['avg_response_time']}")
    
    if stats['top_invalid_keywords']:
        logger.info("Top invalid keywords generated:")
        for kw, count in stats['top_invalid_keywords'][:5]:
            logger.info(f"  - '{kw}': {count} times")
    
    if stats['error_types']:
        logger.info("Error types encountered:")
        for error_type, count in stats['error_types'].items():
            logger.info(f"  - {error_type}: {count} times")
    
    logger.info("==================================")

# Example usage
if __name__ == "__main__":
    # Test the enhancements
    test_response = '''
    {
        "1": {
            "keywords": ["diabetes", "muscle pain", "headache", "nausea"],
            "reasoning": {
                "diabetes": "Common condition",
                "muscle pain": "Patient has pain",
                "headache": "Common symptom",
                "nausea": "May have nausea"
            }
        }
    }
    '''
    
    campaign = ["headache", "nausea", "abdominal pain", "anxiety", "tremor", "rigidity", "dyskinesia"]
    
    enhanced, metrics = enhance_llm_response(test_response, campaign)
    print("Enhanced response:", json.dumps(enhanced, indent=2))
    print("Quality metrics:", metrics) 