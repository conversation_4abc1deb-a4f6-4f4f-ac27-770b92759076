"""
Centralized configuration for keywordsV1 project.
All configurable values are loaded from database with fallback to defaults.
"""

import logging
from src.config_loader import load_config_from_db, get_initial_connection_string

# Setup basic logging for config loading
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("config")

# Configuration Database connection settings - for reading configuration only
CONFIG_DB_DRIVER = "ODBC Driver 18 for SQL Server"  # SQL Server ODBC driver
CONFIG_DB_SERVER = "************"                   # Configuration database server
CONFIG_DB_NAME = "V_820_Dev"                        # Configuration database name
CONFIG_DB_USERNAME = "devAdmin"                     # Configuration database username
CONFIG_DB_PASSWORD = "hOstIR&8l8lWrl=7SlDr"        # Configuration database password
CONFIG_DB_ENCRYPT = "no"                            # Encryption setting
CONFIG_DB_TRUST_CERT = "yes"                        # Trust server certificate

# Keyword Database connection settings - for keyword and patient data operations
# These can be overridden by values from the configuration database
DB_DRIVER = "ODBC Driver 18 for SQL Server"  # SQL Server ODBC driver
DB_SERVER = "************"                   # Keyword database server
DB_NAME = "V_820_Dev"                        # Keyword database name
DB_USERNAME = "devAdmin"                     # Keyword database username
DB_PASSWORD = "hOstIR&8l8lWrl=7SlDr"        # Keyword database password
DB_ENCRYPT = "no"                            # Encryption setting
DB_TRUST_CERT = "yes"                        # Trust server certificate

# Default configuration values (used as fallback if database loading fails)
DEFAULT_CONFIG = {
    # Keyword Database Connection Settings (can override the hardcoded values above)
    "KEYWORD_DB_SERVER": "************",
    "KEYWORD_DB_NAME": "V_820_Dev",
    "KEYWORD_DB_USERNAME": "devAdmin",
    "KEYWORD_DB_PASSWORD": "hOstIR&8l8lWrl=7SlDr",
    
    # API Keys for LLM services
    "GEMINI_API_KEY": "AIzaSyCeRpmqPkfPKVPPV4CuQHyJY4JmtW5pyxk",  # Google Gemini API key
    
    # Runtime limits and processing parameters
    "MAX_TOKENS_PER_RUN": 100000,     # Maximum tokens per LLM call to prevent excessive costs
    "MAX_INPUT_TOKENS": 80000,        # Maximum input tokens per LLM call (leaves room for output)
    "MAX_OUTPUT_TOKENS": 20000,       # Maximum output tokens per LLM call
    "TOKEN_WARNING_THRESHOLD": 0.8,   # Warn when token usage exceeds this percentage of limit (0.8 = 80%)
    "MAX_COST_USD": 5,                # Maximum cost per run in USD
    "COST_WARNING_THRESHOLD": 0.7,    # Warn when cost exceeds this percentage of limit (0.7 = 70%)
    "BATCH_SIZE": 10,                 # Number of patients to process in each batch
    "LLM_TEMPERATURE": 0.2,           # LLM temperature for consistent outputs (lower = more deterministic)
    "LLM_RETRY_ATTEMPTS": 4,          # Number of retry attempts for failed LLM calls
    
    # LLM output configuration
    "REASONING_REQUIRED": True,       # Whether to ask LLM for reasoning behind keyword selection (False = keywords only, faster processing)
    
    # Data filtering options
    "APPOINTMENT_START_DATE": '2024-06-26',  # Only process patients with appointments after this date (optional)
    
    # Input data configuration
    "CAMPAIGN_KEYWORDS_CSV": "data/CampaignKeywords.csv",  # Path to campaign keywords CSV file
    
    # Logging configuration
    "LOG_LEVEL": "INFO",              # Logging level (DEBUG, INFO, WARNING, ERROR)
    "LOG_FILE": "pipeline.log",       # Log file path for persistent logging
}

# Pricing configuration (USD per 1K tokens) - not stored in DB
GEMINI_PRICING = {"input": 0.00025, "output": 0.0005}  # Gemini 1.5 Flash pricing

# Load configuration from database
try:
    # Build connection string for configuration database
    config_conn_str = get_initial_connection_string(
        driver=CONFIG_DB_DRIVER,
        server=CONFIG_DB_SERVER,
        database=CONFIG_DB_NAME,
        username=CONFIG_DB_USERNAME,
        password=CONFIG_DB_PASSWORD,
        encrypt=CONFIG_DB_ENCRYPT,
        trust_cert=CONFIG_DB_TRUST_CERT
    )
    
    # Load config from configuration database with fallback to defaults
    logger.info("Loading configuration from configuration database...")
    db_config = load_config_from_db(config_conn_str, fallback_config=DEFAULT_CONFIG)
    
    # Update keyword database connection settings if provided in config
    if "KEYWORD_DB_SERVER" in db_config:
        DB_SERVER = db_config["KEYWORD_DB_SERVER"]
    if "KEYWORD_DB_NAME" in db_config:
        DB_NAME = db_config["KEYWORD_DB_NAME"]
    if "KEYWORD_DB_USERNAME" in db_config:
        DB_USERNAME = db_config["KEYWORD_DB_USERNAME"]
    if "KEYWORD_DB_PASSWORD" in db_config:
        DB_PASSWORD = db_config["KEYWORD_DB_PASSWORD"]
    
    # Update module-level variables with loaded config
    GEMINI_API_KEY = db_config.get("GEMINI_API_KEY", DEFAULT_CONFIG["GEMINI_API_KEY"])
    MAX_TOKENS_PER_RUN = db_config.get("MAX_TOKENS_PER_RUN", DEFAULT_CONFIG["MAX_TOKENS_PER_RUN"])
    MAX_INPUT_TOKENS = db_config.get("MAX_INPUT_TOKENS", DEFAULT_CONFIG["MAX_INPUT_TOKENS"])
    MAX_OUTPUT_TOKENS = db_config.get("MAX_OUTPUT_TOKENS", DEFAULT_CONFIG["MAX_OUTPUT_TOKENS"])
    TOKEN_WARNING_THRESHOLD = db_config.get("TOKEN_WARNING_THRESHOLD", DEFAULT_CONFIG["TOKEN_WARNING_THRESHOLD"])
    MAX_COST_USD = db_config.get("MAX_COST_USD", DEFAULT_CONFIG["MAX_COST_USD"])
    COST_WARNING_THRESHOLD = db_config.get("COST_WARNING_THRESHOLD", DEFAULT_CONFIG["COST_WARNING_THRESHOLD"])
    BATCH_SIZE = db_config.get("BATCH_SIZE", DEFAULT_CONFIG["BATCH_SIZE"])
    LLM_TEMPERATURE = db_config.get("LLM_TEMPERATURE", DEFAULT_CONFIG["LLM_TEMPERATURE"])
    LLM_RETRY_ATTEMPTS = db_config.get("LLM_RETRY_ATTEMPTS", DEFAULT_CONFIG["LLM_RETRY_ATTEMPTS"])
    REASONING_REQUIRED = db_config.get("REASONING_REQUIRED", DEFAULT_CONFIG["REASONING_REQUIRED"])
    APPOINTMENT_START_DATE = db_config.get("APPOINTMENT_START_DATE", DEFAULT_CONFIG["APPOINTMENT_START_DATE"])
    CAMPAIGN_KEYWORDS_CSV = db_config.get("CAMPAIGN_KEYWORDS_CSV", DEFAULT_CONFIG["CAMPAIGN_KEYWORDS_CSV"])
    LOG_LEVEL = db_config.get("LOG_LEVEL", DEFAULT_CONFIG["LOG_LEVEL"])
    LOG_FILE = db_config.get("LOG_FILE", DEFAULT_CONFIG["LOG_FILE"])
    
    logger.info("Configuration loaded successfully from configuration database")
    logger.info(f"Keyword database will be: {DB_SERVER}/{DB_NAME}")
    
except Exception as e:
    logger.warning(f"Failed to load configuration from database: {e}")
    logger.info("Using default configuration values")
    
    # Use default values
    GEMINI_API_KEY = DEFAULT_CONFIG["GEMINI_API_KEY"]
    MAX_TOKENS_PER_RUN = DEFAULT_CONFIG["MAX_TOKENS_PER_RUN"]
    MAX_INPUT_TOKENS = DEFAULT_CONFIG["MAX_INPUT_TOKENS"]
    MAX_OUTPUT_TOKENS = DEFAULT_CONFIG["MAX_OUTPUT_TOKENS"]
    TOKEN_WARNING_THRESHOLD = DEFAULT_CONFIG["TOKEN_WARNING_THRESHOLD"]
    MAX_COST_USD = DEFAULT_CONFIG["MAX_COST_USD"]
    COST_WARNING_THRESHOLD = DEFAULT_CONFIG["COST_WARNING_THRESHOLD"]
    BATCH_SIZE = DEFAULT_CONFIG["BATCH_SIZE"]
    LLM_TEMPERATURE = DEFAULT_CONFIG["LLM_TEMPERATURE"]
    LLM_RETRY_ATTEMPTS = DEFAULT_CONFIG["LLM_RETRY_ATTEMPTS"]
    REASONING_REQUIRED = DEFAULT_CONFIG["REASONING_REQUIRED"]
    APPOINTMENT_START_DATE = DEFAULT_CONFIG["APPOINTMENT_START_DATE"]
    CAMPAIGN_KEYWORDS_CSV = DEFAULT_CONFIG["CAMPAIGN_KEYWORDS_CSV"]
    LOG_LEVEL = DEFAULT_CONFIG["LOG_LEVEL"]
    LOG_FILE = DEFAULT_CONFIG["LOG_FILE"]

# Configuration validation
def validate_config():
    """
    Validate that all required configuration values are present.

    Checks for the presence of critical configuration values needed
    for the pipeline to function properly. Raises an error if any
    required values are missing.

    Raises:
        EnvironmentError: If any required configuration values are missing.

    Example:
        >>> validate_config()  # Raises error if GEMINI_API_KEY is empty
    """
    missing = []
    if not GEMINI_API_KEY:
        missing.append("GEMINI_API_KEY")
    if not DB_SERVER:
        missing.append("DB_SERVER")
    if not DB_USERNAME:
        missing.append("DB_USERNAME")
    if not DB_PASSWORD:
        missing.append("DB_PASSWORD")
    if missing:
        raise EnvironmentError(f"Missing required config: {', '.join(missing)}")