# Campaign Keywords Pipeline

An AI-powered healthcare data processing pipeline that extracts relevant campaign keywords from patient clinical data using Google's Gemini LLM. The system processes patient records in batches, generates exactly 7 campaign keywords per patient with clinical reasoning, and stores results in a SQL Server database.

## 🚀 Features

- **AI-Powered Keyword Generation**: Uses Google Gemini 1.5 Flash for intelligent keyword extraction
- **Hash-Based Change Detection**: Efficiently processes only patients with modified clinical data
- **Batch Processing**: Handles large datasets with configurable batch sizes
- **Cost Control**: Built-in token counting and cost estimation with configurable limits
- **Robust Error Handling**: Automatic retry logic with exponential backoff
- **Clinical Reasoning**: Generates detailed explanations for each keyword selection
- **Database Integration**: Seamless SQL Server connectivity with transaction safety

## 📋 Table of Contents

- [Installation](#installation)
- [Configuration](#configuration)
- [Usage](#usage)
- [Project Structure](#project-structure)
- [Database Schema](#database-schema)
- [API Costs](#api-costs)
- [Development](#development)
- [Troubleshooting](#troubleshooting)
- [Contributing](#contributing)

## 🛠 Installation

### Prerequisites

- Python 3.11 or higher
- SQL Server with ODBC Driver 18
- Google Cloud account with Gemini API access

### Step 1: Clone the Repository

```bash
git clone https://github.com/your-org/keywordsv1.git
cd keywordsv1
```

### Step 2: Create Virtual Environment

```bash
# Create virtual environment
python -m venv .venv

# Activate virtual environment
# On Windows:
.venv\Scripts\activate
# On macOS/Linux:
source .venv/bin/activate
```

### Step 3: Install Dependencies

```bash
# Install from requirements.txt
pip install -r requirements.txt

# OR install from pyproject.toml
pip install -e .
```

### Step 4: Install SQL Server ODBC Driver

Download and install the Microsoft ODBC Driver 18 for SQL Server from the [official Microsoft website](https://docs.microsoft.com/en-us/sql/connect/odbc/download-odbc-driver-for-sql-server).

## ⚙️ Configuration

The system uses **two separate databases**:
1. **Configuration Database** - Stores dynamic configuration settings
2. **Keyword Database** - Stores patient data and generated keywords

All configuration values are centralized in `config.py`. **Do NOT use a `.env` file.**

### Required Configuration

Edit `config.py` with your specific settings:

```python
# config.py

# Configuration Database (for reading settings)
CONFIG_DB_DRIVER = "ODBC Driver 18 for SQL Server"
CONFIG_DB_SERVER = "config-server\\instance"   # e.g., "localhost\\SQLEXPRESS"
CONFIG_DB_NAME = "ConfigDatabase"
CONFIG_DB_USERNAME = "config_user"
CONFIG_DB_PASSWORD = "config_password"
CONFIG_DB_ENCRYPT = "no"
CONFIG_DB_TRUST_CERT = "yes"

# Keyword Database (for patient data and keywords)
# These can be overridden by values from the configuration database
DB_DRIVER = "ODBC Driver 18 for SQL Server"
DB_SERVER = "data-server\\instance"           # e.g., "localhost\\SQLEXPRESS"
DB_NAME = "PatientDatabase"
DB_USERNAME = "data_user"
DB_PASSWORD = "data_password"
DB_ENCRYPT = "no"                             # Set to "yes" for production
DB_TRUST_CERT = "yes"

# API Keys for LLM services
GEMINI_API_KEY = "your-gemini-api-key-here"  # Get from Google AI Studio

# Runtime limits and processing parameters
MAX_TOKENS_PER_RUN = 900_000    # Maximum tokens per run (cost control)
MAX_COST_USD = 5                # Maximum cost per run in USD
BATCH_SIZE = 25                 # Patients per batch (adjust based on data size)
LLM_TEMPERATURE = 0.2           # Lower = more deterministic outputs
LLM_RETRY_ATTEMPTS = 4          # API retry attempts

# Data filtering (optional)
APPOINTMENT_START_DATE = '2024-06-26'  # Process only recent appointments

# Input data
CAMPAIGN_KEYWORDS_CSV = "data/CampaignKeywords.csv"  # Path to keywords file
```

### Dynamic Configuration via Database

The configuration database can override the keyword database settings through the `EducationalContent_KeywordConfig` table:

```sql
-- Example: Configure keyword database connection via config DB
INSERT INTO dbo.EducationalContent_KeywordConfig (ConfigKey, ConfigValue, ValueType) VALUES
('KEYWORD_DB_SERVER', 'production-server\\instance', 'string'),
('KEYWORD_DB_NAME', 'ProductionPatientDB', 'string'),
('KEYWORD_DB_USERNAME', 'prod_user', 'string'),
('KEYWORD_DB_PASSWORD', 'secure_password', 'string');
```

### Getting a Gemini API Key

1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Create a new API key
4. Copy the key to `config.py`

## 🚀 Usage

### Basic Usage

1. **Prepare your campaign keywords CSV file** at `data/CampaignKeywords.csv`:
   ```csv
   keyword
   diabetes
   hypertension
   asthma
   copd
   heart_disease
   ```

2. **Run the pipeline**:
   ```bash
   python main.py
   ```

### Expected Database Tables

The pipeline expects these SQL Server tables to exist:

- `Patient_Result`: Lab results and observations
- `Patient_Problem`: Medical problems and diagnoses
- `Patient_Medication`: Current medications
- `Patient_Allergy`: Known allergies
- `Appointment`: Patient appointments (for filtering)

### Output

The pipeline creates a `PatientKeywords` table with:
- `patient_id`: Unique patient identifier
- `keywords`: JSON array of 7 selected keywords
- `reasoning`: JSON object with clinical reasoning for each keyword
- `data_hash`: SHA-256 hash for change detection
- `processed_datetime`: Processing timestamp

Example output:
```json
{
  "keywords": ["diabetes", "hypertension", "obesity", "heart_disease", "kidney_disease", "neuropathy", "retinopathy"],
  "reasoning": {
    "diabetes": "Patient diagnosed with Type 2 diabetes, HbA1c 8.2%, prescribed metformin",
    "hypertension": "Blood pressure 150/95 mmHg, prescribed lisinopril for hypertension",
    "obesity": "BMI 32.1, documented obesity affecting diabetes management"
  }
}
```

## 📁 Project Structure

```
keywordsv1/
├── src/                          # Core application modules
│   ├── __init__.py              # Package initialization
│   ├── pipeline.py              # Main pipeline orchestration
│   ├── db.py                    # Database operations
│   └── llm.py                   # LLM integration (Gemini)
├── utils/                       # Utility modules
│   ├── __init__.py              # Package initialization
│   ├── hashing.py               # Data hashing and token counting
│   └── validation.py            # Response validation and parsing
├── data/                        # Data files
│   └── CampaignKeywords.csv     # Campaign keywords list
├── config.py                    # Configuration settings
├── main.py                      # Entry point
├── requirements.txt             # Python dependencies
├── pyproject.toml              # Project metadata and build config
└── README.md                   # This file
```

### Module Descriptions

- **`src/pipeline.py`**: Main orchestration logic, batch processing, and workflow coordination
- **`src/db.py`**: Database connectivity, patient data retrieval, and result storage
- **`src/llm.py`**: Google Gemini integration, prompt engineering, and response handling
- **`utils/hashing.py`**: Data canonicalization, SHA-256 hashing, and token counting
- **`utils/validation.py`**: LLM response validation, JSON parsing, and quality control

## 🗄️ Database Schema

### Required Input Tables

```sql
-- Patient lab results and observations
CREATE TABLE Patient_Result (
    PatientID NVARCHAR(50),
    ResultName NVARCHAR(255),
    ObservationValue NVARCHAR(255),
    ObservationUnit NVARCHAR(50)
);

-- Medical problems and diagnoses
CREATE TABLE Patient_Problem (
    PatientID NVARCHAR(50),
    ProblemName NVARCHAR(255),
    Type NVARCHAR(50),        -- PMH, SocHx, PSH, ROS, FamHx
    DetailText NVARCHAR(MAX)
);

-- Current medications
CREATE TABLE Patient_Medication (
    PatientID NVARCHAR(50),
    MedType NVARCHAR(100),
    MedicationName NVARCHAR(255),
    DoseQuantity NVARCHAR(100)
);

-- Known allergies
CREATE TABLE Patient_Allergy (
    PatientID NVARCHAR(50),
    AllergyName NVARCHAR(255)
);

-- Patient appointments (for filtering)
CREATE TABLE Appointment (
    PatientID NVARCHAR(50),
    AppointmentDate DATETIME
);
```

### Output Table (Auto-Created)

```sql
-- Generated keywords and reasoning (created automatically)
CREATE TABLE PatientKeywords (
    id INT IDENTITY(1,1) PRIMARY KEY,
    patient_id NVARCHAR(50) NOT NULL UNIQUE,
    keywords NVARCHAR(MAX),              -- JSON array of keywords
    reasoning NVARCHAR(MAX),             -- JSON object with reasoning
    data_hash NVARCHAR(64),              -- SHA-256 hash for change detection
    processed_datetime DATETIME DEFAULT GETDATE()
);
```

## 💰 API Costs

### Gemini 1.5 Flash Pricing (as of 2024)

- **Input tokens**: $0.00025 per 1K tokens
- **Output tokens**: $0.0005 per 1K tokens

### Cost Estimation

For typical usage:
- **Input per patient**: ~500-1000 tokens (clinical data + prompt)
- **Output per patient**: ~200-400 tokens (7 keywords + reasoning)
- **Cost per patient**: ~$0.0003-0.0007 USD
- **Cost per 1000 patients**: ~$0.30-0.70 USD

### Built-in Cost Controls

The pipeline includes automatic cost controls:
- `MAX_TOKENS_PER_RUN`: Prevents excessive token usage
- `MAX_COST_USD`: Sets maximum cost per run
- Token counting before API calls
- Batch processing to optimize costs

## 🔧 Development

### Setting Up Development Environment

```bash
# Install development dependencies
pip install -e ".[dev]"

# Run tests
pytest

# Run tests with coverage
pytest --cov=src --cov=utils

# Format code
black .

# Lint code
flake8 src utils

# Type checking
mypy src utils
```

### Running Tests

```bash
# Run all tests
pytest

# Run specific test file
pytest tests/test_pipeline.py

# Run with verbose output
pytest -v

# Run with coverage report
pytest --cov=src --cov=utils --cov-report=html
```

### Code Quality

The project uses:
- **Black**: Code formatting
- **Flake8**: Linting
- **MyPy**: Static type checking
- **Pytest**: Testing framework

## 🐛 Troubleshooting

### Common Issues

#### 1. Database Connection Errors

```
Error: [Microsoft][ODBC Driver 18 for SQL Server][ODBC Driver Manager] Data source name not found
```

**Solution**:
- Verify ODBC Driver 18 is installed
- Check `DB_SERVER` format in `config.py`
- Ensure SQL Server is running and accessible

#### 2. Gemini API Errors

```
Error: 403 Forbidden - API key not valid
```

**Solution**:
- Verify `GEMINI_API_KEY` in `config.py`
- Check API key permissions in Google AI Studio
- Ensure billing is enabled for your Google Cloud project

#### 3. Token Limit Exceeded

```
Error: Token limit exceeded
```

**Solution**:
- Reduce `BATCH_SIZE` in `config.py`
- Increase `MAX_TOKENS_PER_RUN` if budget allows
- Check patient data size and complexity

#### 4. Invalid Keywords in Response

```
Warning: Found 5 invalid keywords in batch
```

**Solution**:
- Review campaign keywords CSV for typos
- Check LLM prompt engineering
- Verify keyword validation logic

### Logging

The pipeline uses Python's logging module. Logs are written to:
- Console (INFO level and above)
- `pipeline.log` file (all levels)

To increase log verbosity, modify `LOG_LEVEL` in `config.py`:
```python
LOG_LEVEL = "DEBUG"  # Shows detailed processing information
```

### Performance Optimization

1. **Batch Size**: Adjust `BATCH_SIZE` based on:
   - Available memory
   - API rate limits
   - Data complexity

2. **Database Indexing**: Add indexes on frequently queried columns:
   ```sql
   CREATE INDEX IX_PatientResult_PatientID ON Patient_Result(PatientID);
   CREATE INDEX IX_PatientKeywords_PatientID ON PatientKeywords(patient_id);
   ```

3. **Hash-Based Processing**: The pipeline automatically skips unchanged patients using SHA-256 hashes.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow PEP 8 style guidelines
- Add type hints to all functions
- Include docstrings for all modules, classes, and functions
- Write tests for new functionality
- Update documentation as needed

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the troubleshooting section above
- Review the logs in `pipeline.log`

## 🔄 Changelog

### v0.1.0 (Current)
- Initial release
- Google Gemini integration
- Hash-based change detection
- Batch processing
- SQL Server connectivity
- Comprehensive documentation

---

**Note**: This pipeline processes sensitive healthcare data. Ensure compliance with HIPAA, GDPR, and other relevant data protection regulations in your deployment.
