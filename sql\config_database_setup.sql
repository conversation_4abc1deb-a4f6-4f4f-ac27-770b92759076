-- =============================================
-- Configuration Database Setup Script
-- =============================================
-- This script creates the configuration table in the configuration database
-- and populates it with initial values.
--
-- The configuration database is separate from the keyword database
-- to allow centralized configuration management.
-- =============================================

-- Create the configuration table
CREATE TABLE dbo.EducationalContent_KeywordConfig (
    ConfigKey NVARCHAR(100) PRIMARY KEY,
    ConfigValue NVARCHAR(500) NOT NULL,
    ValueType NVARCHAR(10) NOT NULL CHECK (ValueType IN ('string', 'int', 'float', 'bool')),
    Description NVARCHAR(500),
    CreatedDate DATETIME DEFAULT GETDATE(),
    ModifiedDate DATETIME DEFAULT GETDATE()
);
GO

-- Insert initial configuration values
INSERT INTO dbo.EducationalContent_KeywordConfig (ConfigKey, ConfigValue, ValueType, Description) VALUES
-- Keyword Database Connection Settings
('KEYWORD_DB_SERVER', '************', 'string', 'Server address for the keyword/patient database'),
('KEYWORD_DB_NAME', 'V_820_Dev', 'string', 'Database name for keyword/patient data'),
('KEYWORD_DB_USERNAME', 'devAdmin', 'string', 'Username for keyword database connection'),
('KEYWORD_DB_PASSWORD', 'hOstIR&8l8lWrl=7SlDr', 'string', 'Password for keyword database connection'),

-- API Keys
('GEMINI_API_KEY', 'AIzaSyCeRpmqPkfPKVPPV4CuQHyJY4JmtW5pyxk', 'string', 'Google Gemini API key for LLM processing'),

-- Processing Limits
('MAX_TOKENS_PER_RUN', '100000', 'int', 'Maximum tokens per LLM call to prevent excessive costs'),
('MAX_INPUT_TOKENS', '80000', 'int', 'Maximum input tokens per LLM call'),
('MAX_OUTPUT_TOKENS', '20000', 'int', 'Maximum output tokens per LLM call'),
('TOKEN_WARNING_THRESHOLD', '0.8', 'float', 'Warn when token usage exceeds this percentage'),
('MAX_COST_USD', '5', 'float', 'Maximum cost per run in USD'),
('COST_WARNING_THRESHOLD', '0.7', 'float', 'Warn when cost exceeds this percentage'),

-- Processing Parameters
('BATCH_SIZE', '10', 'int', 'Number of patients to process in each batch'),
('LLM_TEMPERATURE', '0.2', 'float', 'LLM temperature for output consistency'),
('LLM_RETRY_ATTEMPTS', '4', 'int', 'Number of retry attempts for failed LLM calls'),
('REASONING_REQUIRED', 'true', 'bool', 'Whether to ask LLM for reasoning behind keywords'),

-- Data Filtering
('APPOINTMENT_START_DATE', '2024-06-26', 'string', 'Only process appointments after this date'),

-- File Paths
('CAMPAIGN_KEYWORDS_CSV', 'data/CampaignKeywords.csv', 'string', 'Path to campaign keywords CSV file'),

-- Logging
('LOG_LEVEL', 'INFO', 'string', 'Logging level (DEBUG, INFO, WARNING, ERROR)'),
('LOG_FILE', 'pipeline.log', 'string', 'Log file path for persistent logging');
GO

-- Create update trigger to track modifications
CREATE TRIGGER trg_UpdateModifiedDate
ON dbo.EducationalContent_KeywordConfig
AFTER UPDATE
AS
BEGIN
    UPDATE dbo.EducationalContent_KeywordConfig
    SET ModifiedDate = GETDATE()
    WHERE ConfigKey IN (SELECT ConfigKey FROM inserted);
END;
GO

-- View to show current configuration
CREATE VIEW vw_CurrentConfiguration AS
SELECT 
    ConfigKey,
    ConfigValue,
    ValueType,
    Description,
    ModifiedDate
FROM dbo.EducationalContent_KeywordConfig
ORDER BY 
    CASE 
        WHEN ConfigKey LIKE 'KEYWORD_DB_%' THEN 1
        WHEN ConfigKey LIKE '%API_KEY%' THEN 2
        WHEN ConfigKey LIKE 'MAX_%' THEN 3
        ELSE 4
    END,
    ConfigKey;
GO

-- Example queries for managing configuration
/*
-- View all configuration values
SELECT * FROM vw_CurrentConfiguration;

-- Update keyword database connection
UPDATE dbo.EducationalContent_KeywordConfig
SET ConfigValue = 'production-server'
WHERE ConfigKey = 'KEYWORD_DB_SERVER';

-- Change batch size
UPDATE dbo.EducationalContent_KeywordConfig
SET ConfigValue = '25'
WHERE ConfigKey = 'BATCH_SIZE';

-- Disable reasoning to speed up processing
UPDATE dbo.EducationalContent_KeywordConfig
SET ConfigValue = 'false'
WHERE ConfigKey = 'REASONING_REQUIRED';
*/ 