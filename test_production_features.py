"""
Test script to demonstrate the three production-ready features:
1. Health checks
2. Log rotation
3. Connection pooling

This script can be run to verify that all improvements are working correctly.
"""

import time
import logging
import requests
import threading
import os
from src.db_pool import get_keyword_db_pool, keyword_db_connection
from src.logging_config import setup_pipeline_logging
from src.health_check import start_health_api

def test_health_checks():
    """Test the health check API endpoints."""
    print("\n=== Testing Health Check API ===")
    
    # Give the health API a moment to start
    time.sleep(2)
    
    # Test main health endpoint
    try:
        response = requests.get("http://localhost:8080/health")
        print(f"Health Check Status: {response.status_code}")
        print(f"Health Response: {response.json()}")
        
        # Test liveness endpoint
        response = requests.get("http://localhost:8080/health/live")
        print(f"\nLiveness Check Status: {response.status_code}")
        print(f"Liveness Response: {response.json()}")
        
        # Test readiness endpoint
        response = requests.get("http://localhost:8080/health/ready")
        print(f"\nReadiness Check Status: {response.status_code}")
        print(f"Readiness Response: {response.json()}")
        
    except Exception as e:
        print(f"Error testing health checks: {e}")

def test_log_rotation():
    """Test the log rotation functionality."""
    print("\n\n=== Testing Log Rotation ===")
    
    # Setup logging
    loggers = setup_pipeline_logging()
    
    # Generate enough log entries to test rotation
    print("Generating log entries...")
    for i in range(100):
        loggers['main'].info(f"Test log entry {i+1}/100 - " + "x" * 100)
        
        if i % 20 == 0:
            loggers['health'].info(f"Health check log entry {i}")
            loggers['database'].info(f"Database operation log entry {i}")
            loggers['llm'].info(f"LLM operation log entry {i}")
    
    # Check if log files exist
    print("\nLog files created:")
    log_files = [
        "pipeline.log",
        "pipeline_health.log",
        "pipeline_database.log",
        "pipeline_llm.log"
    ]
    
    for log_file in log_files:
        if os.path.exists(log_file):
            size = os.path.getsize(log_file) / 1024  # KB
            print(f"  - {log_file}: {size:.2f} KB")
        else:
            print(f"  - {log_file}: Not found")

def test_connection_pooling():
    """Test the database connection pooling."""
    print("\n\n=== Testing Connection Pooling ===")
    
    # Get the pool instance
    pool = get_keyword_db_pool()
    
    # Initial statistics
    print("Initial pool statistics:")
    stats = pool.get_stats()
    for key, value in stats.items():
        print(f"  - {key}: {value}")
    
    # Test multiple concurrent connections
    def test_connection(thread_id):
        """Function to test connection in a thread."""
        try:
            with keyword_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                cursor.close()
                print(f"Thread {thread_id}: Connection successful, result: {result}")
                time.sleep(0.5)  # Simulate some work
        except Exception as e:
            print(f"Thread {thread_id}: Connection failed: {e}")
    
    # Create threads to test concurrent connections
    print("\nTesting concurrent connections...")
    threads = []
    for i in range(10):
        thread = threading.Thread(target=test_connection, args=(i,))
        threads.append(thread)
        thread.start()
    
    # Wait for all threads to complete
    for thread in threads:
        thread.join()
    
    # Final statistics
    print("\nFinal pool statistics:")
    stats = pool.get_stats()
    for key, value in stats.items():
        print(f"  - {key}: {value}")
    
    # Test connection reuse
    print("\nTesting connection reuse...")
    start_time = time.time()
    for i in range(5):
        with keyword_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.close()
    
    elapsed_time = time.time() - start_time
    print(f"5 sequential connections completed in {elapsed_time:.2f} seconds")
    print("(Connection pooling should make this faster than creating new connections)")

def main():
    """Run all tests."""
    print("=== Testing Production-Ready Features ===")
    print("This script demonstrates:")
    print("1. Health check API")
    print("2. Log rotation")
    print("3. Connection pooling")
    
    # Start health check API
    print("\nStarting health check API on port 8080...")
    start_health_api(port=8080)
    
    # Run tests
    test_health_checks()
    test_log_rotation()
    test_connection_pooling()
    
    print("\n\n=== All Tests Complete ===")
    print("Check the following:")
    print("1. Health API is accessible at http://localhost:8080/health")
    print("2. Log files are created with rotation support")
    print("3. Database connections are pooled and reused efficiently")

if __name__ == "__main__":
    main()
    
    # Keep the script running so health API stays available
    print("\nPress Ctrl+C to exit...")
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nShutting down...") 