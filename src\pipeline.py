"""
Main pipeline module for processing appointment data and generating campaign keywords.

This module orchestrates the entire appointment keyword extraction pipeline:
1. Loads campaign keywords from CSV
2. Fetches appointment data from database in batches
3. Retrieves clinical data for each patient
4. Generates keywords using LLM analysis
5. Updates keywords in the appointment table using stored procedures

The pipeline processes appointments from the dbo.Appointment_EducationalContent_Keyword table
and updates the Keyword column with generated keywords.
"""

import logging
import json
import signal
import sys
import config
from src import db
from src import llm
from utils import hashing
from utils import validation
import os
import warnings
warnings.filterwarnings("ignore", message="pandas only supports SQLAlchemy connectable")
from datetime import datetime
import time

# Get logger from logging config
logger = logging.getLogger("patient_pipeline")

# Global flag for graceful shutdown
shutdown_requested = False


def datetime_serializer(obj):
    """JSON serializer for datetime objects."""
    if isinstance(obj, datetime):
        return obj.isoformat()
    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")


def signal_handler(signum, frame):
    """Handle shutdown signals gracefully."""
    global shutdown_requested
    logger.info(f"Received signal {signum}. Requesting graceful shutdown...")
    shutdown_requested = True

# Register signal handlers for graceful shutdown
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

def process_batch(
    appointment_json,
    campaign,
):
    """
    Process a batch of appointments through Gemini LLM to generate campaign keywords.

    This function takes a batch of appointment data and generates campaign keywords
    for each appointment using Google's Gemini LLM service. It includes token counting,
    cost estimation, and response validation.

    Args:
        appointment_json (Dict[str, Any]): Dictionary mapping appointment keys to their clinical data
        campaign (List[str]): List of valid campaign keywords to choose from

    Returns:
        Dict[str, Any]: Parsed and validated LLM response containing keywords for each appointment

    Raises:
        RuntimeError: If token or cost limits are exceeded
        Exception: If LLM call fails or response validation fails

    Example:
        >>> appt_data = {"12345_67890": {"Results": [...], "Problems": [...]}}
        >>> keywords = ["diabetes", "hypertension", "asthma"]
        >>> result = process_batch(appt_data, keywords)
    """
    logger.info(f"Processing batch with {len(appointment_json)} appointments using Gemini LLM")

    try:
        # Build the prompt with appointment data and campaign keywords
        logger.debug("Building prompt...")
        prompt = llm.build_prompt(appointment_json, campaign)
        logger.debug("Prompt built successfully")
    except Exception as e:
        logger.error(f"Error building prompt: {e}")
        raise

    try:
        # Count tokens and check limits before making expensive LLM call
        logger.debug("Counting tokens...")
        in_tokens = hashing.count_tokens(prompt)
        logger.debug(f"Token count: {in_tokens}")
        llm.check_run_limits(in_tokens, out_tok_est=8_000)
    except Exception as e:
        logger.error(f"Error during token counting/limit check: {e}")
        raise

    try:
        # Make the Gemini LLM call
        logger.info("Calling Gemini LLM...")
        raw_response = llm.query_gemini(prompt)
        logger.info("LLM call complete. Parsing response...")
    except Exception as e:
        logger.error(f"Error during LLM call: {e}")
        raise
    
    # Try enhanced parsing first
    try:
        from src.llm_improvements import enhance_llm_response, log_llm_statistics
        parsed, quality_metrics = enhance_llm_response(raw_response, campaign)
        
        # Log quality metrics if they indicate issues
        if quality_metrics["quality_score"] < 90:
            logger.warning(f"Response quality score: {quality_metrics['quality_score']}/100")
            if quality_metrics["invalid_keywords"]:
                logger.warning(f"Invalid keywords detected and corrected: {len(quality_metrics['invalid_keywords'])}")
            if quality_metrics["vague_reasoning"]:
                logger.warning(f"Vague reasoning instances: {len(quality_metrics['vague_reasoning'])}")
        
        logger.info("Batch processed with enhanced validation.")
        return parsed
        
    except Exception as e:
        logger.warning(f"Enhanced processing failed, falling back to standard validation: {e}")
        # Fall back to standard validation
        parsed = validation.validate_and_parse(raw_response, campaign)
        logger.info("Batch processed and validated.")
        return parsed

def main():
    """
    Main pipeline function that orchestrates the entire keyword extraction process.

    This function implements the core pipeline logic:
    1. Loads campaign keywords from CSV file
    2. Processes appointments in batches with pagination
    3. Retrieves clinical data for each patient
    4. Generates keywords using LLM for all appointments
    5. Updates keywords in the appointment table using stored procedures

    The pipeline processes all appointments in the dbo.Appointment_EducationalContent_Keyword table.

    Raises:
        FileNotFoundError: If campaign keywords CSV file is not found
        Exception: If database connection or LLM processing fails

    Example:
        >>> main()  # Processes all appointments in the database
    """
    logger.info("🚀 Starting Appointment Keyword Pipeline")
    
    # Log system information
    import psutil
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('/')
    logger.info(f"System resources - Memory: {memory.percent:.1f}% used, Disk: {disk.percent:.1f}% used")

    # Load campaign keywords from CSV file
    csv_file = config.CAMPAIGN_KEYWORDS_CSV
    campaign_kw = []
    try:
        with open(csv_file, newline="", encoding="utf-8") as f:
            import csv
            rdr = csv.reader(f)
            # Skip the first row (header)
            next(rdr, None)

            # Collect all non-empty cells from all rows and columns
            seen_keywords = set()
            for row in rdr:
                for cell in row:
                    # Clean and normalize the keyword
                    keyword = cell.strip().lower()
                    if keyword and keyword not in seen_keywords:
                        campaign_kw.append(keyword)
                        seen_keywords.add(keyword)

        if not campaign_kw:
            raise ValueError("No valid campaign keywords found in CSV file")

        logger.info("Loaded %d unique campaign keywords.", len(campaign_kw))
        logger.debug("Sample keywords: %s", campaign_kw[:10])

    except FileNotFoundError:
        logger.error("Campaign keywords CSV file not found: %s", csv_file)
        raise
    except Exception as e:
        logger.error("Error loading campaign keywords from CSV: %s", e)
        raise

    # Initialize pagination and tracking variables
    offset = 0
    total_processed = 0
    consecutive_empty_batches = 0
    max_empty_batches = 2

    # Main processing loop - continues until all appointments are processed
    error_count = 0
    max_errors = 5
    
    while not shutdown_requested:
        try:
            # Fetch the next batch of appointment records
            with db.sql_connection() as conn:
                db.ensure_tables(conn)  # Verify database tables exist
                appointments = db.fetch_appointment_batch(conn, offset, batch=config.BATCH_SIZE)

            # Exit loop if no more appointments to process
            if not appointments:
                consecutive_empty_batches += 1
                if consecutive_empty_batches >= max_empty_batches:
                    logger.warning("Reached maximum consecutive empty batches (%d). Exiting to prevent infinite loop.", max_empty_batches)
                    break
                logger.info("No appointments found at offset %d. Trying next batch...", offset)
                offset += config.BATCH_SIZE
                continue
            else:
                consecutive_empty_batches = 0  # Reset counter when we find appointments

        except Exception as e:
            logger.error("Error fetching appointments at offset %d: %s", offset, e)
            error_count += 1
            # Continue processing instead of stopping
            offset += config.BATCH_SIZE
            continue

        # Fetch detailed clinical data for each appointment
        # Enable hash-based change detection to skip unchanged patients
        with db.sql_connection() as conn:
            appointment_data = db.fetch_batch_appointment_data(conn, appointments, skip_unchanged=True)

        # Limit patient data if it exceeds token threshold (4000 tokens per patient)
        import json
        TOKEN_LIMIT_PER_PATIENT = 5000
        for appt_key, pdata in appointment_data.items():
            tokens = hashing.count_tokens(json.dumps(
                pdata,
                default=datetime_serializer
            ))
            if tokens > TOKEN_LIMIT_PER_PATIENT and pdata.get("Medications"):
                # Keep only the latest medication (assume last in list is latest)
                pdata["Medications"] = [pdata["Medications"][-1]]

        # Skip this batch if no clinical data found
        if not appointment_data:
            logger.info("Offset %d: no clinical data found for appointments, skipping.", offset)
            offset += config.BATCH_SIZE
            continue

        # Check for shutdown before expensive LLM processing
        if shutdown_requested:
            logger.info("Shutdown requested. Stopping before LLM processing.")
            break

        # Process appointments through LLM with specific error handling
        try:
            results = process_batch(appointment_data, campaign_kw)
            # Don't reset error count - we want to track total errors
        except (ConnectionError, TimeoutError) as e:
            logger.error("Network/timeout error during LLM processing at offset %d: %s. Continuing to next batch.", offset, e)
            error_count += 1
            # Continue processing instead of stopping
            offset += config.BATCH_SIZE
            continue
        except ValueError as e:
            if "token limit" in str(e).lower():
                logger.error("Token limit exceeded at offset %d: %s. Skipping batch.", offset, e)
            else:
                logger.error("Validation error during LLM processing at offset %d: %s. Skipping batch.", offset, e)
            error_count += 1
            # Continue processing instead of stopping
            offset += config.BATCH_SIZE
            continue
        except Exception as e:
            logger.error("Unexpected error during LLM processing at offset %d: %s. Skipping batch.", offset, e)
            error_count += 1
            # Continue processing instead of stopping
            offset += config.BATCH_SIZE
            continue

        # Map LLM results back to appointment data and update database
        with db.sql_connection() as conn:
            db.update_appointment_keywords(conn, results, appointment_data)

        # Update progress tracking
        total_processed += len(appointment_data)
        logger.info("Processed %d appointments (cumulative %d).", len(appointment_data), total_processed)
        offset += config.BATCH_SIZE

        # Check for shutdown after processing
        if shutdown_requested:
            logger.info("Shutdown requested. Stopping after processing current batch.")
            break

    # Pipeline completion summary
    if shutdown_requested:
        logger.info("Pipeline stopped gracefully due to shutdown request. Processed %d appointments total.", total_processed)
    else:
        # Log final statistics and cleanup
        total_cost = llm.check_run_limits.cost_so_far
        logger.info(f"Pipeline completed successfully. Processed {total_processed} appointments total.")
        logger.info(f"Total errors encountered but continued: {error_count}")
        if error_count > 0:
            logger.info("Despite errors, pipeline continued processing all available batches.")
        logger.info(f"Total API cost: ${total_cost:.2f}")
    
    # Log LLM performance statistics if available
    try:
        from src.llm_improvements import log_llm_statistics
        log_llm_statistics()
    except Exception as e:
        logger.debug(f"Could not log LLM statistics: {e}")