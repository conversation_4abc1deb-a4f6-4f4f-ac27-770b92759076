"""
Database connection pool for efficient connection management.

This module provides connection pooling to:
- Reduce connection overhead
- Manage connection lifecycle
- Handle connection retries
- Monitor connection health
- Support both configuration and keyword databases
"""

import pyodbc
import threading
import queue
import time
import logging
from contextlib import contextmanager
from typing import Optional, Dict, Any
import config

logger = logging.getLogger("database")

class ConnectionPool:
    """
    Thread-safe database connection pool.
    
    Manages a pool of database connections to reduce overhead and
    improve performance for concurrent database operations.
    """
    
    def __init__(
        self,
        connection_string: str,
        pool_name: str = "default",
        min_connections: int = 2,
        max_connections: int = 10,
        connection_timeout: int = 30,
        idle_timeout: int = 3600,  # 1 hour
        retry_attempts: int = 3,
        retry_delay: int = 1
    ):
        """
        Initialize connection pool.
        
        Args:
            connection_string: Database connection string
            pool_name: Name for this pool (for logging)
            min_connections: Minimum connections to maintain
            max_connections: Maximum connections allowed
            connection_timeout: Timeout for new connections
            idle_timeout: Time before closing idle connections
            retry_attempts: Number of connection retry attempts
            retry_delay: Delay between retry attempts
        """
        self.connection_string = connection_string
        self.pool_name = pool_name
        self.min_connections = min_connections
        self.max_connections = max_connections
        self.connection_timeout = connection_timeout
        self.idle_timeout = idle_timeout
        self.retry_attempts = retry_attempts
        self.retry_delay = retry_delay
        
        # Pool management
        self._pool = queue.Queue(maxsize=max_connections)
        self._all_connections = []
        self._connection_info = {}  # Track connection metadata
        self._lock = threading.RLock()
        self._closed = False
        
        # Statistics
        self.stats = {
            'created': 0,
            'active': 0,
            'idle': 0,
            'failed': 0,
            'total_requests': 0,
            'total_wait_time': 0
        }
        
        # Initialize minimum connections
        self._initialize_pool()
        
        # Start maintenance thread
        self._maintenance_thread = threading.Thread(
            target=self._maintenance_loop,
            daemon=True
        )
        self._maintenance_thread.start()
        
        logger.info(f"Connection pool '{pool_name}' initialized with min={min_connections}, max={max_connections}")
    
    def _initialize_pool(self):
        """Create initial connections for the pool."""
        for _ in range(self.min_connections):
            try:
                conn = self._create_connection()
                if conn:
                    self._pool.put(conn)
            except Exception as e:
                logger.warning(f"Failed to create initial connection: {e}")
    
    def _create_connection(self) -> Optional[pyodbc.Connection]:
        """
        Create a new database connection with retry logic.
        
        Returns:
            New connection or None if failed
        """
        for attempt in range(self.retry_attempts):
            try:
                # Create connection
                conn = pyodbc.connect(
                    self.connection_string,
                    timeout=self.connection_timeout,
                    autocommit=False
                )
                
                # Store connection metadata
                with self._lock:
                    self._all_connections.append(conn)
                    self._connection_info[id(conn)] = {
                        'created_at': time.time(),
                        'last_used': time.time(),
                        'use_count': 0
                    }
                    self.stats['created'] += 1
                
                logger.debug(f"Created new connection for pool '{self.pool_name}'")
                return conn
                
            except pyodbc.Error as e:
                self.stats['failed'] += 1
                if attempt < self.retry_attempts - 1:
                    logger.warning(f"Connection attempt {attempt + 1} failed: {e}. Retrying...")
                    time.sleep(self.retry_delay * (2 ** attempt))  # Exponential backoff
                else:
                    logger.error(f"Failed to create connection after {self.retry_attempts} attempts: {e}")
                    return None
    
    def _is_connection_healthy(self, conn: pyodbc.Connection) -> bool:
        """
        Check if a connection is still healthy.
        
        Args:
            conn: Connection to check
            
        Returns:
            True if healthy, False otherwise
        """
        try:
            # Execute a simple query to test connection
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            return True
        except Exception:
            return False
    
    def _close_connection(self, conn: pyodbc.Connection):
        """
        Close a connection and clean up metadata.
        
        Args:
            conn: Connection to close
        """
        try:
            conn.close()
        except Exception:
            pass
        
        with self._lock:
            if conn in self._all_connections:
                self._all_connections.remove(conn)
            conn_id = id(conn)
            if conn_id in self._connection_info:
                del self._connection_info[conn_id]
    
    @contextmanager
    def get_connection(self):
        """
        Get a connection from the pool.
        
        This is a context manager that automatically returns the connection
        to the pool when done.
        
        Yields:
            Database connection
            
        Raises:
            RuntimeError: If pool is closed or no connections available
        """
        if self._closed:
            raise RuntimeError(f"Connection pool '{self.pool_name}' is closed")
        
        conn = None
        start_time = time.time()
        
        try:
            # Update statistics
            self.stats['total_requests'] += 1
            
            # Try to get existing connection from pool
            try:
                conn = self._pool.get(timeout=5)
                wait_time = time.time() - start_time
                self.stats['total_wait_time'] += wait_time
                
                # Check if connection is healthy
                if not self._is_connection_healthy(conn):
                    logger.warning("Unhealthy connection found, creating new one")
                    self._close_connection(conn)
                    conn = None
                
            except queue.Empty:
                logger.debug("No available connections in pool, creating new one")
                conn = None
            
            # Create new connection if needed
            if conn is None:
                with self._lock:
                    if len(self._all_connections) < self.max_connections:
                        conn = self._create_connection()
                    else:
                        # Wait longer for a connection to become available
                        logger.warning(f"Connection pool '{self.pool_name}' at maximum capacity")
                        conn = self._pool.get(timeout=30)
            
            if conn is None:
                raise RuntimeError(f"Unable to get connection from pool '{self.pool_name}'")
            
            # Update connection metadata
            with self._lock:
                conn_id = id(conn)
                if conn_id in self._connection_info:
                    self._connection_info[conn_id]['last_used'] = time.time()
                    self._connection_info[conn_id]['use_count'] += 1
                self.stats['active'] += 1
                self.stats['idle'] = self._pool.qsize()
            
            yield conn
            
        finally:
            # Return connection to pool
            if conn:
                try:
                    # Reset connection state
                    conn.rollback()
                    
                    # Return to pool if healthy
                    if self._is_connection_healthy(conn):
                        self._pool.put(conn)
                    else:
                        logger.warning("Closing unhealthy connection")
                        self._close_connection(conn)
                        
                    with self._lock:
                        self.stats['active'] -= 1
                        self.stats['idle'] = self._pool.qsize()
                        
                except Exception as e:
                    logger.error(f"Error returning connection to pool: {e}")
                    self._close_connection(conn)
    
    def _maintenance_loop(self):
        """
        Background thread that maintains the connection pool.
        
        - Closes idle connections
        - Ensures minimum connections
        - Updates statistics
        """
        while not self._closed:
            try:
                time.sleep(60)  # Run maintenance every minute
                
                with self._lock:
                    current_time = time.time()
                    connections_to_close = []
                    
                    # Check for idle connections
                    for conn in list(self._all_connections):
                        conn_id = id(conn)
                        if conn_id in self._connection_info:
                            info = self._connection_info[conn_id]
                            idle_time = current_time - info['last_used']
                            
                            # Close connections that have been idle too long
                            if idle_time > self.idle_timeout and len(self._all_connections) > self.min_connections:
                                connections_to_close.append(conn)
                    
                    # Close idle connections
                    for conn in connections_to_close:
                        try:
                            # Try to remove from pool
                            temp_conns = []
                            while not self._pool.empty():
                                temp_conn = self._pool.get_nowait()
                                if temp_conn != conn:
                                    temp_conns.append(temp_conn)
                                else:
                                    self._close_connection(conn)
                                    logger.debug(f"Closed idle connection in pool '{self.pool_name}'")
                                    break
                            
                            # Put connections back
                            for temp_conn in temp_conns:
                                self._pool.put_nowait(temp_conn)
                                
                        except queue.Empty:
                            pass
                    
                    # Ensure minimum connections
                    current_total = len(self._all_connections)
                    if current_total < self.min_connections:
                        for _ in range(self.min_connections - current_total):
                            try:
                                conn = self._create_connection()
                                if conn:
                                    self._pool.put_nowait(conn)
                            except queue.Full:
                                break
                            except Exception as e:
                                logger.error(f"Maintenance thread error: {e}")
                    
                    # Update statistics
                    self.stats['idle'] = self._pool.qsize()
                    
            except Exception as e:
                logger.error(f"Error in maintenance loop: {e}")
    
    def close(self):
        """Close all connections and shut down the pool."""
        logger.info(f"Closing connection pool '{self.pool_name}'")
        self._closed = True
        
        # Close all connections
        with self._lock:
            while not self._pool.empty():
                try:
                    conn = self._pool.get_nowait()
                    conn.close()
                except Exception:
                    pass
            
            for conn in list(self._all_connections):
                try:
                    conn.close()
                except Exception:
                    pass
            
            self._all_connections.clear()
            self._connection_info.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get pool statistics.
        
        Returns:
            Dictionary with pool statistics
        """
        with self._lock:
            return {
                **self.stats,
                'total_connections': len(self._all_connections),
                'pool_size': self._pool.qsize(),
                'avg_wait_time': self.stats['total_wait_time'] / max(1, self.stats['total_requests'])
            }

# Global connection pools
_pools = {}
_pools_lock = threading.Lock()

def get_pool(
    connection_string: str,
    pool_name: str = "default",
    **kwargs
) -> ConnectionPool:
    """
    Get or create a connection pool.
    
    Args:
        connection_string: Database connection string
        pool_name: Name for the pool
        **kwargs: Additional arguments for ConnectionPool
        
    Returns:
        ConnectionPool instance
    """
    with _pools_lock:
        if pool_name not in _pools:
            _pools[pool_name] = ConnectionPool(
                connection_string=connection_string,
                pool_name=pool_name,
                **kwargs
            )
        return _pools[pool_name]

def close_all_pools():
    """Close all connection pools."""
    with _pools_lock:
        for pool_name, pool in _pools.items():
            try:
                pool.close()
            except Exception as e:
                logger.error(f"Error closing pool '{pool_name}': {e}")
        _pools.clear()

# Convenience functions for the two databases
def get_config_db_pool() -> ConnectionPool:
    """Get connection pool for configuration database."""
    conn_str = (
        f"DRIVER={{{config.CONFIG_DB_DRIVER}}};"
        f"SERVER={config.CONFIG_DB_SERVER};"
        f"DATABASE={config.CONFIG_DB_NAME};"
        f"UID={config.CONFIG_DB_USERNAME};"
        f"PWD={config.CONFIG_DB_PASSWORD};"
        f"Encrypt={config.CONFIG_DB_ENCRYPT};"
        f"TrustServerCertificate={config.CONFIG_DB_TRUST_CERT};"
    )
    
    return get_pool(
        connection_string=conn_str,
        pool_name="config_db",
        min_connections=1,
        max_connections=5
    )

def get_keyword_db_pool() -> ConnectionPool:
    """Get connection pool for keyword database."""
    conn_str = (
        f"DRIVER={{{config.DB_DRIVER}}};"
        f"SERVER={config.DB_SERVER};"
        f"DATABASE={config.DB_NAME};"
        f"UID={config.DB_USERNAME};"
        f"PWD={config.DB_PASSWORD};"
        f"Encrypt={config.DB_ENCRYPT};"
        f"TrustServerCertificate={config.DB_TRUST_CERT};"
    )
    
    return get_pool(
        connection_string=conn_str,
        pool_name="keyword_db",
        min_connections=5,
        max_connections=20
    )

@contextmanager
def config_db_connection():
    """Get a connection from the configuration database pool."""
    pool = get_config_db_pool()
    with pool.get_connection() as conn:
        yield conn

@contextmanager
def keyword_db_connection():
    """Get a connection from the keyword database pool."""
    pool = get_keyword_db_pool()
    with pool.get_connection() as conn:
        yield conn

# Example usage
if __name__ == "__main__":
    logging.basicConfig(level=logging.DEBUG)
    
    # Test connection pool
    pool = get_keyword_db_pool()
    
    # Test getting connections
    for i in range(5):
        with keyword_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            print(f"Connection {i}: {result}")
            cursor.close()
    
    # Print statistics
    print("Pool statistics:", pool.get_stats())
    
    # Clean up
    close_all_pools() 