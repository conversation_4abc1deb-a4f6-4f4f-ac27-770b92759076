"""
Configuration loader module for reading settings from the configuration database.

This module provides functionality to load configuration values from
the EducationalContent_KeywordConfig table in a separate configuration database,
with support for type conversion and fallback to default values.

The configuration database is separate from the keyword/patient database to allow:
- Centralized configuration management across environments
- Dynamic updates without code changes
- Separation of concerns between configuration and operational data

Key features:
- Loads configuration from a dedicated configuration database
- Can override keyword database connection settings
- Supports multiple data types (string, int, float, bool)
- Provides fallback values if database is unavailable
"""

import logging
from typing import Any, Dict, Optional
import pyodbc

logger = logging.getLogger("config_loader")


def _convert_value(value: str, value_type: str) -> Any:
    """
    Convert a string value to the appropriate Python type.

    Args:
        value: The string value from the database
        value_type: The type to convert to (int, float, bool, string)

    Returns:
        The converted value

    Raises:
        ValueError: If conversion fails
    """
    if value_type == 'string':
        return value
    elif value_type == 'int':
        return int(value)
    elif value_type == 'float':
        return float(value)
    elif value_type == 'bool':
        return value.lower() in ('true', 'yes', '1', 'on')
    else:
        logger.warning(f"Unknown value type '{value_type}', treating as string")
        return value


def load_config_from_db(conn_str: str, fallback_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Load configuration values from the EducationalContent_KeywordConfig table.

    Args:
        conn_str: Database connection string
        fallback_config: Optional dictionary of fallback values if database read fails

    Returns:
        Dictionary of configuration key-value pairs

    Raises:
        pyodbc.Error: If database connection or query fails
    """
    config_dict = {}
    fallback_config = fallback_config or {}

    try:
        # Connect to database
        with pyodbc.connect(conn_str, timeout=30) as conn:
            with conn.cursor() as cursor:
                # Query all configuration values
                cursor.execute("""
                    SELECT ConfigKey, ConfigValue, ValueType
                    FROM dbo.EducationalContent_KeywordConfig
                """)
                
                rows = cursor.fetchall()
                
                if not rows:
                    logger.warning("No configuration values found in database")
                    return fallback_config
                
                # Process each configuration row
                for row in rows:
                    config_key = row[0]
                    config_value = row[1]
                    value_type = row[2]
                    
                    try:
                        # Convert value to appropriate type
                        converted_value = _convert_value(config_value, value_type)
                        config_dict[config_key] = converted_value
                        logger.debug(f"Loaded config: {config_key} = {converted_value} ({value_type})")
                    except Exception as e:
                        logger.error(f"Failed to convert config value for {config_key}: {e}")
                        # Use fallback value if available
                        if config_key in fallback_config:
                            config_dict[config_key] = fallback_config[config_key]
                            logger.info(f"Using fallback value for {config_key}")
                
        logger.info(f"Successfully loaded {len(config_dict)} configuration values from database")
        
        # Add any missing values from fallback config
        for key, value in fallback_config.items():
            if key not in config_dict:
                config_dict[key] = value
                logger.debug(f"Added fallback config: {key} = {value}")
        
        return config_dict
        
    except pyodbc.Error as e:
        logger.error(f"Database error while loading configuration: {e}")
        if fallback_config:
            logger.warning("Using fallback configuration values")
            return fallback_config
        raise
    except Exception as e:
        logger.error(f"Unexpected error while loading configuration: {e}")
        if fallback_config:
            logger.warning("Using fallback configuration values")
            return fallback_config
        raise


def get_initial_connection_string(
    driver: str = "ODBC Driver 18 for SQL Server",
    server: str = "************",
    database: str = "V_820_Dev",
    username: str = "devAdmin",
    password: str = "hOstIR&8l8lWrl=7SlDr",
    encrypt: str = "no",
    trust_cert: str = "yes"
) -> str:
    """
    Build initial connection string for loading configuration.
    
    These are minimal hardcoded values needed to connect to the database
    and load the rest of the configuration.
    
    Args:
        driver: ODBC driver name
        server: Database server address
        database: Database name
        username: Database username
        password: Database password
        encrypt: Encryption setting
        trust_cert: Trust server certificate setting
        
    Returns:
        Connection string for pyodbc
    """
    return (
        f"DRIVER={{{driver}}};"
        f"SERVER={server};"
        f"DATABASE={database};"
        f"UID={username};"
        f"PWD={password};"
        f"Encrypt={encrypt};"
        f"TrustServerCertificate={trust_cert};"
    ) 