# Core dependencies for Campaign Keywords Pipeline
# AI/ML Libraries
google-generativeai>=0.3.0    # Google Gemini API client for LLM integration

# Database connectivity
pyodbc>=4.0.39                # SQL Server ODBC driver for database operations

# Data processing
pandas>=2.0.0                 # Data manipulation and analysis

# Utility libraries
tenacity>=8.2.0               # Retry logic with exponential backoff for API calls
tiktoken>=0.5.0               # Token counting for accurate cost estimation

# Health monitoring and operations (NEW)
flask>=3.0.0                  # Web framework for health check API
psutil>=5.9.0                 # System and process utilities for resource monitoring

# Testing (NEW)
requests>=2.31.0              # HTTP library for testing health check endpoints

# Development dependencies (optional)
# Uncomment the following lines for development environment:
# pytest>=7.0.0               # Testing framework
# pytest-cov>=4.0.0           # Test coverage reporting
# black>=23.0.0               # Code formatting
# flake8>=6.0.0               # Code linting
# mypy>=1.0.0                 # Static type checking