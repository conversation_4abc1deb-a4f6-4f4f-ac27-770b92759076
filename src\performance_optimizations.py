"""
Performance optimization module for the Campaign Keywords Pipeline.

This module provides optimizations for:
- Batch processing efficiency
- Caching of unchanged patient results
- Parallel processing capabilities
- Resource monitoring and throttling
"""

import logging
import time
import threading
from typing import Dict, List, Any, Optional
from collections import deque
from datetime import datetime, timedelta
import concurrent.futures
import psutil

logger = logging.getLogger("patient_pipeline")

class BatchOptimizer:
    """Optimize batch sizes based on system resources and performance metrics."""
    
    def __init__(self, min_batch_size: int = 5, max_batch_size: int = 50):
        self.min_batch_size = min_batch_size
        self.max_batch_size = max_batch_size
        self.current_batch_size = min(25, max_batch_size)  # Start with moderate size
        
        # Performance tracking
        self.processing_times = deque(maxlen=10)  # Last 10 batch processing times
        self.error_rates = deque(maxlen=10)  # Last 10 batch error rates
        self.memory_usage = deque(maxlen=10)  # Memory usage during processing
        
    def get_optimal_batch_size(self) -> int:
        """
        Calculate optimal batch size based on system resources and performance.
        
        Returns:
            Optimal batch size
        """
        # Check available memory
        memory = psutil.virtual_memory()
        memory_factor = 1.0
        
        if memory.percent > 80:
            memory_factor = 0.5  # Reduce batch size if memory is high
        elif memory.percent > 60:
            memory_factor = 0.8
        
        # Check processing time trends
        if len(self.processing_times) >= 3:
            avg_time = sum(self.processing_times) / len(self.processing_times)
            if avg_time > 30:  # If batches take more than 30 seconds
                self.current_batch_size = max(self.min_batch_size, int(self.current_batch_size * 0.8))
            elif avg_time < 10:  # If batches are processed quickly
                self.current_batch_size = min(self.max_batch_size, int(self.current_batch_size * 1.2))
        
        # Apply memory factor
        optimal_size = int(self.current_batch_size * memory_factor)
        optimal_size = max(self.min_batch_size, min(self.max_batch_size, optimal_size))
        
        logger.info(f"Optimal batch size calculated: {optimal_size} (memory: {memory.percent:.1f}%)")
        return optimal_size
    
    def record_batch_performance(self, batch_size: int, processing_time: float, 
                               error_count: int, memory_before: float, memory_after: float):
        """Record performance metrics for a batch."""
        self.processing_times.append(processing_time)
        self.error_rates.append(error_count / batch_size if batch_size > 0 else 0)
        self.memory_usage.append(memory_after - memory_before)
        
        # Log performance metrics
        avg_time = sum(self.processing_times) / len(self.processing_times)
        avg_error_rate = sum(self.error_rates) / len(self.error_rates)
        
        logger.debug(f"Batch performance - Size: {batch_size}, Time: {processing_time:.2f}s, "
                    f"Errors: {error_count}, Avg time: {avg_time:.2f}s, "
                    f"Avg error rate: {avg_error_rate:.2%}")

class ResultCache:
    """Cache for patient keyword results to avoid reprocessing unchanged data."""
    
    def __init__(self, max_cache_size: int = 1000, ttl_hours: int = 24):
        self.cache = {}  # {patient_id: (keywords, reasoning, hash, timestamp)}
        self.max_cache_size = max_cache_size
        self.ttl = timedelta(hours=ttl_hours)
        self.hits = 0
        self.misses = 0
        self._lock = threading.RLock()
        
    def get(self, patient_id: int, data_hash: str) -> Optional[Dict[str, Any]]:
        """
        Get cached results for a patient if the data hash matches.
        
        Args:
            patient_id: Patient identifier
            data_hash: Hash of current patient data
            
        Returns:
            Cached results if valid, None otherwise
        """
        with self._lock:
            if patient_id in self.cache:
                keywords, reasoning, cached_hash, timestamp = self.cache[patient_id]
                
                # Check if hash matches and not expired
                if cached_hash == data_hash and datetime.now() - timestamp < self.ttl:
                    self.hits += 1
                    logger.debug(f"Cache hit for patient {patient_id}")
                    return {"keywords": keywords, "reasoning": reasoning}
            
            self.misses += 1
            return None
    
    def set(self, patient_id: int, data_hash: str, keywords: List[str], reasoning: Dict[str, str]):
        """Cache results for a patient."""
        with self._lock:
            # Implement LRU eviction if cache is full
            if len(self.cache) >= self.max_cache_size:
                # Remove oldest entry
                oldest_patient = min(self.cache.keys(), 
                                   key=lambda p: self.cache[p][3])
                del self.cache[oldest_patient]
            
            self.cache[patient_id] = (keywords, reasoning, data_hash, datetime.now())
            logger.debug(f"Cached results for patient {patient_id}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._lock:
            total_requests = self.hits + self.misses
            hit_rate = (self.hits / total_requests * 100) if total_requests > 0 else 0
            
            return {
                "cache_size": len(self.cache),
                "hits": self.hits,
                "misses": self.misses,
                "hit_rate": f"{hit_rate:.1f}%",
                "total_requests": total_requests
            }

class ParallelProcessor:
    """Process multiple operations in parallel for better performance."""
    
    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
        
    def fetch_patient_data_parallel(self, conn, appointments: List[Dict[str, Any]], 
                                  fetch_func) -> Dict[str, Any]:
        """
        Fetch patient data in parallel for better performance.
        
        Args:
            conn: Database connection
            appointments: List of appointments to process
            fetch_func: Function to fetch patient data
            
        Returns:
            Dictionary of patient data
        """
        results = {}
        
        # Split appointments into chunks for parallel processing
        chunk_size = max(1, len(appointments) // self.max_workers)
        chunks = [appointments[i:i + chunk_size] for i in range(0, len(appointments), chunk_size)]
        
        futures = []
        for chunk in chunks:
            future = self.executor.submit(self._fetch_chunk, conn, chunk, fetch_func)
            futures.append(future)
        
        # Collect results
        for future in concurrent.futures.as_completed(futures):
            try:
                chunk_results = future.result()
                results.update(chunk_results)
            except Exception as e:
                logger.error(f"Error in parallel fetch: {e}")
        
        return results
    
    def _fetch_chunk(self, conn, appointments: List[Dict[str, Any]], fetch_func) -> Dict[str, Any]:
        """Fetch data for a chunk of appointments."""
        chunk_results = {}
        
        for appt in appointments:
            try:
                patient_id = appt["PatientID"]
                appt_id = appt["ApptID"]
                appt_key = f"{patient_id}_{appt_id}"
                
                data = fetch_func(conn, patient_id)
                if data:
                    data["AppointmentInfo"] = {
                        "PatientID": patient_id,
                        "ApptID": appt_id,
                        "ApptNo": appt.get("ApptNo"),
                        "AppDate": appt.get("AppDate")
                    }
                    chunk_results[appt_key] = data
            except Exception as e:
                logger.error(f"Error fetching data for patient {patient_id}: {e}")
        
        return chunk_results
    
    def shutdown(self):
        """Shutdown the executor."""
        self.executor.shutdown(wait=True)

class ResourceMonitor:
    """Monitor system resources and provide throttling recommendations."""
    
    def __init__(self):
        self.high_resource_count = 0
        self.last_check = datetime.now()
        
    def should_throttle(self) -> bool:
        """
        Check if processing should be throttled based on system resources.
        
        Returns:
            True if throttling is recommended
        """
        # Only check every 5 seconds to avoid overhead
        if datetime.now() - self.last_check < timedelta(seconds=5):
            return False
        
        self.last_check = datetime.now()
        
        # Check CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # Check memory usage
        memory = psutil.virtual_memory()
        
        # Check disk I/O
        disk_io = psutil.disk_io_counters()
        
        # Determine if resources are constrained
        if cpu_percent > 90 or memory.percent > 85:
            self.high_resource_count += 1
            logger.warning(f"High resource usage - CPU: {cpu_percent}%, Memory: {memory.percent}%")
            
            # Throttle if consistently high
            if self.high_resource_count >= 3:
                return True
        else:
            self.high_resource_count = 0
        
        return False
    
    def get_wait_time(self) -> float:
        """Get recommended wait time based on resource usage."""
        memory = psutil.virtual_memory()
        cpu_percent = psutil.cpu_percent(interval=0.1)
        
        # Base wait time
        wait_time = 0.0
        
        if memory.percent > 90:
            wait_time = 5.0
        elif memory.percent > 85:
            wait_time = 2.0
        elif memory.percent > 80:
            wait_time = 1.0
        
        if cpu_percent > 95:
            wait_time = max(wait_time, 3.0)
        elif cpu_percent > 90:
            wait_time = max(wait_time, 1.0)
        
        return wait_time

# Global instances
batch_optimizer = BatchOptimizer()
result_cache = ResultCache()
resource_monitor = ResourceMonitor()

def log_performance_statistics():
    """Log current performance statistics."""
    logger.info("=== Performance Statistics ===")
    
    # Cache statistics
    cache_stats = result_cache.get_statistics()
    logger.info(f"Cache performance - Size: {cache_stats['cache_size']}, "
               f"Hit rate: {cache_stats['hit_rate']}, "
               f"Total requests: {cache_stats['total_requests']}")
    
    # System resources
    memory = psutil.virtual_memory()
    cpu_percent = psutil.cpu_percent(interval=1)
    disk = psutil.disk_usage('/')
    
    logger.info(f"System resources - CPU: {cpu_percent}%, "
               f"Memory: {memory.percent}% ({memory.available / (1024**3):.1f}GB available), "
               f"Disk: {disk.percent}% used")
    
    logger.info("=============================")

# Example usage
if __name__ == "__main__":
    # Test the optimizations
    logging.basicConfig(level=logging.DEBUG)
    
    # Test batch optimizer
    optimizer = BatchOptimizer()
    print(f"Initial batch size: {optimizer.get_optimal_batch_size()}")
    
    # Simulate some processing
    optimizer.record_batch_performance(25, 15.5, 2, 60.0, 65.0)
    optimizer.record_batch_performance(25, 18.2, 1, 65.0, 68.0)
    optimizer.record_batch_performance(25, 22.1, 3, 68.0, 72.0)
    
    print(f"Optimized batch size: {optimizer.get_optimal_batch_size()}")
    
    # Test cache
    cache = ResultCache()
    cache.set(123, "hash123", ["keyword1", "keyword2"], {"keyword1": "reason1"})
    result = cache.get(123, "hash123")
    print(f"Cache result: {result}")
    print(f"Cache stats: {cache.get_statistics()}")
    
    # Log performance
    log_performance_statistics() 